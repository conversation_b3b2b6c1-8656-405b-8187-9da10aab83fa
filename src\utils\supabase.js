import 'react-native-url-polyfill/auto';
import { createClient } from '@supabase/supabase-js';
import Constants from 'expo-constants';

// Get environment variables from Expo Constants
const supabaseUrl = Constants.expoConfig?.extra?.REACT_APP_SUPABASE_URL || process.env.REACT_APP_SUPABASE_URL;
const supabaseAnonKey = Constants.expoConfig?.extra?.REACT_APP_ANON_KEY || process.env.REACT_APP_ANON_KEY;

// Validate that environment variables are present
if (!supabaseUrl) {
  throw new Error('Missing REACT_APP_SUPABASE_URL environment variable');
}

if (!supabaseAnonKey) {
  throw new Error('Missing REACT_APP_ANON_KEY environment variable');
}

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Export individual functions for convenience
export const auth = supabase.auth;
export const db = supabase;

// Helper function to check connection
export const checkSupabaseConnection = async () => {
  try {
    const { data, error } = await supabase.from('_health_check').select('*').limit(1);
    if (error) {
      console.log('Supabase connection test (expected if no health check table):', error.message);
      return true; // This is expected if no specific table exists
    }
    console.log('Supabase connected successfully');
    return true;
  } catch (error) {
    console.error('Supabase connection failed:', error);
    return false;
  }
};

// Form submission functions
export const submitPatientDemographicSheet = async (formData) => {
  try {
    const { data, error } = await supabase
      .from('patient_demographic_sheets')
      .insert([{
        full_legal_name: formData.fullLegalName,
        date: formData.date,
        phone: formData.phone,
        email: formData.email,
        address: formData.address,
        city_state_zip: formData.cityStateZip,
        age: formData.age ? parseInt(formData.age) : null,
        date_of_birth: formData.dob,
        ssn: formData.ssn,
        gender: formData.gender,
        active_duty_service_member: formData.activeDutyServiceMember,
        dod_benefit: formData.dodBenefit,
        current_employer: formData.currentEmployer,
        spouse_name: formData.spouseName,
        spouse_age: formData.spouseAge ? parseInt(formData.spouseAge) : null,
        spouse_dob: formData.spouseDob,
        spouse_ssn: formData.spouseSsn,
        spouse_employer: formData.spouseEmployer,
        referring_provider: formData.referringProvider,
        primary_health_insurance: formData.primaryHealthInsurance,
        policy: formData.policy,
        group_number: formData.group,
        known_medical_conditions: formData.knownMedicalConditions,
        drug_allergies: formData.drugAllergies,
        current_medications: formData.currentMedications,
        submitted_at: new Date().toISOString()
      }])
      .select();

    return { data, error };
  } catch (error) {
    console.error('Error submitting patient demographic sheet:', error);
    return { data: null, error };
  }
};

export const submitPHQ9 = async (formData) => {
  try {
    const { data, error } = await supabase
      .from('phq9_submissions')
      .insert([{
        responses: formData.responses,
        total_score: Object.values(formData.responses).reduce((sum, val) => sum + parseInt(val || 0), 0),
        submitted_at: new Date().toISOString()
      }])
      .select();

    return { data, error };
  } catch (error) {
    console.error('Error submitting PHQ-9:', error);
    return { data: null, error };
  }
};

export const submitBDI = async (formData) => {
  try {
    // Calculate total score for BDI
    let totalScore = 0;
    for (let i = 0; i < 21; i++) {
      const response = formData.responses[i];
      if (response) {
        // Handle special cases for sleep and appetite questions
        if (i === 15 || i === 17) { // Sleep and Appetite questions
          const value = response.replace(/[a-z]/g, ''); // Remove letters from values like "1a", "1b"
          totalScore += parseInt(value);
        } else {
          totalScore += parseInt(response);
        }
      }
    }

    const { data, error } = await supabase
      .from('bdi_submissions')
      .insert([{
        responses: formData.responses,
        total_score: totalScore,
        submitted_at: new Date().toISOString()
      }])
      .select();

    return { data, error };
  } catch (error) {
    console.error('Error submitting BDI:', error);
    return { data: null, error };
  }
};

export const submitMedicalHistory = async (formData) => {
  try {
    const { data, error } = await supabase
      .from('medical_history_submissions')
      .insert([{
        medical_conditions: formData.medicalConditions,
        suicidal_thoughts: formData.suicidalThoughts,
        attempts: formData.attempts,
        suicidal_explanation: formData.suicidalExplanation,
        previous_psychiatrist: formData.previousPsychiatrist,
        psychiatric_hospitalizations: formData.psychiatricHospitalizations,
        legal_charges: formData.legalCharges,
        legal_explanation: formData.legalExplanation,
        allergies: formData.allergies,
        signature: formData.signature,
        submitted_at: new Date().toISOString()
      }])
      .select();

    return { data, error };
  } catch (error) {
    console.error('Error submitting medical history:', error);
    return { data: null, error };
  }
};

export const submitPreCertMedList = async (formData) => {
  try {
    const { data, error } = await supabase
      .from('pre_cert_med_list_submissions')
      .insert([{
        name: formData.name,
        date_of_birth: formData.dateOfBirth,
        selected_medications: formData.selectedMedications,
        medication_details: formData.medicationDetails,
        submitted_at: new Date().toISOString()
      }])
      .select();

    return { data, error };
  } catch (error) {
    console.error('Error submitting pre-cert med list:', error);
    return { data: null, error };
  }
};