{"name": "tms_app", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:android:preview": "eas build --platform android --profile preview", "build:android:production": "eas build --platform android --profile production", "lint": "eslint .", "test": "jest"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-native-community/netinfo": "11.4.1", "@supabase/supabase-js": "^2.49.8", "emailjs-com": "^3.2.0", "expo": "~53.0.9", "expo-constants": "~17.1.6", "expo-haptics": "~14.1.4", "expo-image": "^2.1.7", "expo-linear-gradient": "^14.1.4", "expo-router": "~5.0.7", "expo-splash-screen": "^0.30.8", "expo-status-bar": "~2.2.3", "expo-updates": "~0.28.13", "expo-video": "~2.1.9", "react": "19.0.0", "react-native": "0.79.2", "react-native-drawer-layout": "^4.1.8", "react-native-gesture-handler": "~2.24.0", "react-native-polyfill-globals": "^3.1.0", "react-native-reanimated": "^3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/helper-define-polyfill-provider": "^0.6.4", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/preset-env": "^7.27.2", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "babel-plugin-module-resolver": "^5.0.2", "babel-preset-expo": "~13.0.0", "typescript": "~5.8.3"}, "private": true}