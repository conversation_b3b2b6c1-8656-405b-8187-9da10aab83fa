// Test script for database integration
// This file can be used to test database connections and form submissions

import { 
  submitPatientDemographicSheet,
  submitPHQ9,
  submitBDI,
  submitMedicalHistory,
  submitPreCertMedList,
  checkSupabaseConnection 
} from './supabase';

// Test data for each form type
const testPatientDemographicData = {
  fullLegalName: 'Test Patient',
  date: new Date().toISOString().split('T')[0],
  phone: '555-0123',
  email: '<EMAIL>',
  address: '123 Test Street',
  cityStateZip: 'Test City, TS 12345',
  age: '30',
  dob: '1994-01-01',
  ssn: '',
  gender: 'M',
  activeDutyServiceMember: 'N',
  dodBenefit: '',
  currentEmployer: 'Test Company',
  spouseName: '',
  spouseAge: '',
  spouseDob: null,
  spouseSsn: '',
  spouseEmployer: '',
  referringProvider: 'Dr. Test',
  primaryHealthInsurance: 'Test Insurance',
  policy: 'POL123',
  group: 'GRP456',
  knownMedicalConditions: 'None',
  drugAllergies: 'None',
  currentMedications: 'None'
};

const testPHQ9Data = {
  responses: {
    0: '1',
    1: '2',
    2: '0',
    3: '1',
    4: '0',
    5: '1',
    6: '0',
    7: '0',
    8: '0'
  }
};

const testBDIData = {
  responses: {
    0: '1',
    1: '0',
    2: '2',
    3: '1',
    4: '0',
    5: '1',
    6: '0',
    7: '0',
    8: '0',
    9: '1',
    10: '0',
    11: '1',
    12: '0',
    13: '0',
    14: '1',
    15: '0',
    16: '0',
    17: '0',
    18: '1',
    19: '0',
    20: '0'
  }
};

const testMedicalHistoryData = {
  medicalConditions: {
    'ASTHMA': true,
    'ANXIETY': true,
    'HEADACHE': false
  },
  suicidalThoughts: 'No',
  attempts: 'No',
  suicidalExplanation: '',
  previousPsychiatrist: 'Dr. Previous',
  psychiatricHospitalizations: 'None',
  legalCharges: 'No',
  legalExplanation: '',
  allergies: 'Peanuts',
  signature: 'Test Patient'
};

const testPreCertMedListData = {
  name: 'Test Patient',
  dateOfBirth: '1994-01-01',
  selectedMedications: {
    'SSRI': {
      'Sertraline (Zoloft)': true,
      'Fluoxetine (Prozac)': false
    },
    'SNRI': {
      'Venlafaxine (Effexor) IR/XR': true
    }
  },
  medicationDetails: {
    'SSRI_Sertraline (Zoloft)': {
      dose: '50mg',
      startDate: '2023-01-01',
      endDate: '2023-06-01',
      reasonForDiscontinuing: 'Side effects'
    },
    'SNRI_Venlafaxine (Effexor) IR/XR': {
      dose: '75mg',
      startDate: '2023-06-01',
      endDate: null,
      reasonForDiscontinuing: ''
    }
  }
};

// Test functions
export const testDatabaseConnection = async () => {
  console.log('Testing database connection...');
  try {
    const isConnected = await checkSupabaseConnection();
    console.log('Database connection test:', isConnected ? 'SUCCESS' : 'FAILED');
    return isConnected;
  } catch (error) {
    console.error('Database connection test failed:', error);
    return false;
  }
};

export const testPatientDemographicSubmission = async () => {
  console.log('Testing Patient Demographic Sheet submission...');
  try {
    const { data, error } = await submitPatientDemographicSheet(testPatientDemographicData);
    if (error) {
      console.error('Patient Demographic submission failed:', error);
      return false;
    }
    console.log('Patient Demographic submission SUCCESS:', data);
    return true;
  } catch (error) {
    console.error('Patient Demographic submission error:', error);
    return false;
  }
};

export const testPHQ9Submission = async () => {
  console.log('Testing PHQ-9 submission...');
  try {
    const { data, error } = await submitPHQ9(testPHQ9Data);
    if (error) {
      console.error('PHQ-9 submission failed:', error);
      return false;
    }
    console.log('PHQ-9 submission SUCCESS:', data);
    return true;
  } catch (error) {
    console.error('PHQ-9 submission error:', error);
    return false;
  }
};

export const testBDISubmission = async () => {
  console.log('Testing BDI submission...');
  try {
    const { data, error } = await submitBDI(testBDIData);
    if (error) {
      console.error('BDI submission failed:', error);
      return false;
    }
    console.log('BDI submission SUCCESS:', data);
    return true;
  } catch (error) {
    console.error('BDI submission error:', error);
    return false;
  }
};

export const testMedicalHistorySubmission = async () => {
  console.log('Testing Medical History submission...');
  try {
    const { data, error } = await submitMedicalHistory(testMedicalHistoryData);
    if (error) {
      console.error('Medical History submission failed:', error);
      return false;
    }
    console.log('Medical History submission SUCCESS:', data);
    return true;
  } catch (error) {
    console.error('Medical History submission error:', error);
    return false;
  }
};

export const testPreCertMedListSubmission = async () => {
  console.log('Testing Pre-Cert Med List submission...');
  try {
    const { data, error } = await submitPreCertMedList(testPreCertMedListData);
    if (error) {
      console.error('Pre-Cert Med List submission failed:', error);
      return false;
    }
    console.log('Pre-Cert Med List submission SUCCESS:', data);
    return true;
  } catch (error) {
    console.error('Pre-Cert Med List submission error:', error);
    return false;
  }
};

// Run all tests
export const runAllDatabaseTests = async () => {
  console.log('=== Starting Database Integration Tests ===');
  
  const results = {
    connection: false,
    patientDemographic: false,
    phq9: false,
    bdi: false,
    medicalHistory: false,
    preCertMedList: false
  };

  // Test connection first
  results.connection = await testDatabaseConnection();
  
  if (!results.connection) {
    console.log('Database connection failed. Skipping form tests.');
    return results;
  }

  // Test each form submission
  results.patientDemographic = await testPatientDemographicSubmission();
  results.phq9 = await testPHQ9Submission();
  results.bdi = await testBDISubmission();
  results.medicalHistory = await testMedicalHistorySubmission();
  results.preCertMedList = await testPreCertMedListSubmission();

  // Summary
  console.log('=== Test Results Summary ===');
  console.log('Database Connection:', results.connection ? 'PASS' : 'FAIL');
  console.log('Patient Demographic:', results.patientDemographic ? 'PASS' : 'FAIL');
  console.log('PHQ-9:', results.phq9 ? 'PASS' : 'FAIL');
  console.log('BDI:', results.bdi ? 'PASS' : 'FAIL');
  console.log('Medical History:', results.medicalHistory ? 'PASS' : 'FAIL');
  console.log('Pre-Cert Med List:', results.preCertMedList ? 'PASS' : 'FAIL');

  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  console.log(`Overall: ${passedTests}/${totalTests} tests passed`);

  return results;
};

// Export test data for manual testing
export const testData = {
  patientDemographic: testPatientDemographicData,
  phq9: testPHQ9Data,
  bdi: testBDIData,
  medicalHistory: testMedicalHistoryData,
  preCertMedList: testPreCertMedListData
};
