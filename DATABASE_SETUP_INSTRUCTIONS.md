# TMS Application Database Setup Instructions

This document provides comprehensive instructions for setting up the database for the TMS Application forms in Supabase.

## Overview

The TMS Application now stores all form submissions in a Supabase database. The following forms are supported:

1. **Patient Demographic Sheet** - Personal and contact information
2. **PHQ-9** - Patient Health Questionnaire (Depression screening)
3. **BDI** - Beck Depression Inventory
4. **Medical History** - Medical conditions and psychiatric history
5. **Pre-Certification Medication List** - Medication history and details

## Database Setup

### Step 1: Access Supabase SQL Editor

1. Log in to your Supabase dashboard
2. Navigate to your TMS project
3. Go to the **SQL Editor** tab in the left sidebar

### Step 2: Run the Setup Script

1. Copy the entire contents of `supabase_setup.sql`
2. Paste it into the SQL Editor
3. Click **Run** to execute the script

The script will create:
- 5 main tables for form submissions
- Indexes for better performance
- Row Level Security (RLS) policies
- Triggers for automatic timestamp updates
- A statistics view for monitoring submissions

### Step 3: Verify Setup

After running the script, verify the setup by checking:

1. **Tables Created**: Go to **Table Editor** and confirm these tables exist:
   - `patient_demographic_sheets`
   - `phq9_submissions`
   - `bdi_submissions`
   - `medical_history_submissions`
   - `pre_cert_med_list_submissions`

2. **RLS Enabled**: Each table should have Row Level Security enabled

3. **Test Query**: Run this query in SQL Editor to verify:
   ```sql
   SELECT * FROM form_submission_stats;
   ```

## Environment Configuration

Ensure your Supabase environment variables are properly configured:

### In your `.env` file (for local development):
```
REACT_APP_SUPABASE_URL=your_supabase_project_url
REACT_APP_ANON_KEY=your_supabase_anon_key
```

### In Expo app.json (for production):
```json
{
  "expo": {
    "extra": {
      "REACT_APP_SUPABASE_URL": "your_supabase_project_url",
      "REACT_APP_ANON_KEY": "your_supabase_anon_key"
    }
  }
}
```

## Database Schema Details

### 1. Patient Demographic Sheets Table
Stores comprehensive patient information including:
- Personal details (name, age, contact info)
- Address information
- Military service status
- Employment information
- Spouse information
- Healthcare provider details
- Medical conditions and medications

### 2. PHQ-9 Submissions Table
Stores depression screening responses:
- `responses`: JSONB object with question responses (0-3 scale)
- `total_score`: Calculated total score
- Automatic timestamp tracking

### 3. BDI Submissions Table
Stores Beck Depression Inventory responses:
- `responses`: JSONB object with 21 question responses
- `total_score`: Calculated total score (handles special scoring for sleep/appetite)
- Automatic timestamp tracking

### 4. Medical History Submissions Table
Stores medical and psychiatric history:
- Medical conditions (as JSONB)
- Suicidal ideation assessment
- Previous psychiatric care
- Legal history
- Allergies
- Digital signature

### 5. Pre-Certification Medication List Table
Stores medication history:
- Patient identification
- Selected medications (as JSONB)
- Detailed medication information (doses, dates, reasons for discontinuation)

## Security Considerations

### Row Level Security (RLS)
All tables have RLS enabled with public access policies for form submissions. This is appropriate for a patient intake system where:
- Patients need to submit forms without authentication
- Data is protected at the database level
- Access can be restricted in the future if needed

### Data Privacy
- Sensitive data like SSN is stored but should be handled carefully
- Consider implementing data encryption for highly sensitive fields
- Regular backups should be configured
- Access logs should be monitored

## Monitoring and Analytics

### Form Submission Statistics
Use the `form_submission_stats` view to monitor:
- Total submissions per form type
- Daily submission counts
- Weekly submission trends

Example query:
```sql
SELECT * FROM form_submission_stats ORDER BY total_submissions DESC;
```

### Individual Form Analysis
Query specific forms for detailed analysis:

```sql
-- PHQ-9 score distribution
SELECT total_score, COUNT(*) as count 
FROM phq9_submissions 
GROUP BY total_score 
ORDER BY total_score;

-- Recent submissions
SELECT form_type, today_submissions, week_submissions 
FROM form_submission_stats;
```

## Troubleshooting

### Common Issues

1. **Connection Errors**
   - Verify environment variables are correct
   - Check Supabase project URL and anon key
   - Ensure network connectivity

2. **Permission Errors**
   - Verify RLS policies are created
   - Check that anon role has necessary permissions
   - Confirm tables exist and are accessible

3. **Data Validation Errors**
   - Check required fields are being submitted
   - Verify data types match schema
   - Ensure JSONB fields contain valid JSON

### Testing Database Connection

Use this code snippet to test the database connection:

```javascript
import { checkSupabaseConnection } from '../src/utils/supabase';

// Test connection
checkSupabaseConnection().then(connected => {
  console.log('Database connected:', connected);
});
```

## Backup and Maintenance

### Regular Backups
1. Set up automatic backups in Supabase dashboard
2. Export data regularly for local backups
3. Test restore procedures

### Database Maintenance
1. Monitor table sizes and performance
2. Review and optimize queries
3. Update indexes as needed
4. Clean up old test data

## Support

For issues with database setup or form submissions:

1. Check Supabase logs in the dashboard
2. Review console errors in the app
3. Verify network connectivity
4. Test with sample data

## Next Steps

After successful setup:

1. Test each form submission
2. Verify data appears in tables
3. Set up monitoring and alerts
4. Configure backup procedures
5. Train staff on data access procedures

The database is now ready to store all TMS application form submissions securely and efficiently.
